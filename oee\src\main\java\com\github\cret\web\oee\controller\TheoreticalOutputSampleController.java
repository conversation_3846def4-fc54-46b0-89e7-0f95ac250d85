package com.github.cret.web.oee.controller;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.excel.EasyExcel;
import jakarta.servlet.http.HttpServletResponse;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.domain.theoreticalOutput.TheoreticalOutputSample;
import com.github.cret.web.oee.service.TheoreticalOutputSampleService;

@RestController
@RequestMapping("/theoretical-output-sample")
public class TheoreticalOutputSampleController {

	private final TheoreticalOutputSampleService theoreticalOutputSampleService;

	public TheoreticalOutputSampleController(TheoreticalOutputSampleService theoreticalOutputSampleService) {
		this.theoreticalOutputSampleService = theoreticalOutputSampleService;
	}

	@RequestMapping("/list")
	public List<TheoreticalOutputSample> getTheoreticalOutputSamples() {
		return theoreticalOutputSampleService.getTheoreticalOutputSamples();
	}

	@PostMapping("/page")
	public PageList<TheoreticalOutputSample> page(@RequestBody PageableParam<TheoreticalOutputSample> param) {
		return theoreticalOutputSampleService.page(param);
	}

	@PostMapping("/export")
	public void exportExcel(HttpServletResponse response, @RequestBody TheoreticalOutputSample param)
			throws IOException {
		response.setContentType("application/vnd.ms-excel");
		response.setCharacterEncoding("utf-8");
		String fileName = URLEncoder.encode("理论CT缺失数据", StandardCharsets.UTF_8);
		response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");

		List<TheoreticalOutputSample> dataList = theoreticalOutputSampleService.findList(param);
		EasyExcel.write(response.getOutputStream(), TheoreticalOutputSample.class).sheet("理论产出样品").doWrite(dataList);
	}

}
