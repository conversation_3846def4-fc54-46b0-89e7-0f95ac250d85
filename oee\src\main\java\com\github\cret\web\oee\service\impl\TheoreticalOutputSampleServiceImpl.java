package com.github.cret.web.oee.service.impl;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.domain.theoreticalOutput.TheoreticalOutputSample;
import com.github.cret.web.oee.enums.DeviceCategory;
import com.github.cret.web.oee.service.DeviceService;
import com.github.cret.web.oee.service.TheoreticalOutputSampleService;
import com.github.cret.web.oee.service.TheoreticalOutputService;

@Service
public class TheoreticalOutputSampleServiceImpl implements TheoreticalOutputSampleService {

	private final TheoreticalOutputService theoreticalOutputService;

	private final DeviceService deviceService;

	public TheoreticalOutputSampleServiceImpl(TheoreticalOutputService theoreticalOutputService,
			DeviceService deviceService) {
		this.theoreticalOutputService = theoreticalOutputService;
		this.deviceService = deviceService;
	}

	@Override
	public List<TheoreticalOutputSample> getTheoreticalOutputSamples() {
		List<Device> smtDevices = deviceService.getDevicesByCategory(DeviceCategory.SMT);

		return smtDevices.stream()
			.flatMap(device -> theoreticalOutputService.getTheoreticalOutputSamples(device).stream())
			.distinct()
			.sorted(Comparator.comparing(TheoreticalOutputSample::getLineCode)
				.thenComparing(TheoreticalOutputSample::getProductModel)
				.thenComparing(TheoreticalOutputSample::getDeviceSort))
			.collect(Collectors.toList());
	}

	@Override
	public PageList<TheoreticalOutputSample> page(PageableParam<TheoreticalOutputSample> param) {
		// 获取所有数据
		List<TheoreticalOutputSample> allSamples = getTheoreticalOutputSamples();

		// 应用搜索条件过滤
		List<TheoreticalOutputSample> filteredSamples = allSamples;
		if (param.getSearchParams() != null) {
			TheoreticalOutputSample searchParams = param.getSearchParams();
			filteredSamples = allSamples.stream()
				.filter(sample -> matchesSearchCriteria(sample, searchParams))
				.collect(Collectors.toList());
		}

		// 计算分页参数
		int pageNumber = param.getPageData().getPageNumber();
		int pageSize = param.getPageData().getPageSize();
		int totalElements = filteredSamples.size();

		// ================== 新增的健壮性检查 ==================
		// 如果请求的页码超出了筛选后数据的实际范围，则重置为查询第一页
		// (例如，请求第3页，但筛选后总共只有1页数据)
		if (pageNumber > 0 && pageNumber * pageSize >= totalElements) {
			pageNumber = 0; // 重置为第一页 (页码通常从0开始)
		}
		// ======================================================

		int startIndex = pageNumber * pageSize;
		// 确保 startIndex 不会超过列表大小，以防列表为空的边缘情况
		if (startIndex > totalElements) {
			startIndex = totalElements;
		}
		int endIndex = Math.min(startIndex + pageSize, totalElements);

		// 获取当前页数据
		List<TheoreticalOutputSample> pageContent = Collections.emptyList();
		// 仅当 startIndex 小于 endIndex 时才进行截取，避免空列表或边界情况出错
		if (startIndex < endIndex) {
			pageContent = filteredSamples.subList(startIndex, endIndex);
		}

		// 构建分页结果
		PageList<TheoreticalOutputSample> pageList = new PageList<>();
		pageList.setTotal((long) totalElements);
		pageList.setList(pageContent);
		pageList.setHasNext(endIndex < totalElements);

		return pageList;
	}

	@Override
	public List<TheoreticalOutputSample> findList(TheoreticalOutputSample param) {
		// 获取所有数据
		List<TheoreticalOutputSample> allSamples = getTheoreticalOutputSamples();

		// 应用搜索条件过滤
		if (param != null) {
			return allSamples.stream()
				.filter(sample -> matchesSearchCriteria(sample, param))
				.collect(Collectors.toList());
		}

		return allSamples;
	}

	private boolean matchesSearchCriteria(TheoreticalOutputSample sample, TheoreticalOutputSample searchParams) {
		if (searchParams.getLineCode() != null && !searchParams.getLineCode().isEmpty()) {
			if (!sample.getLineCode().contains(searchParams.getLineCode())) {
				return false;
			}
		}
		if (searchParams.getDeviceCode() != null && !searchParams.getDeviceCode().isEmpty()) {
			if (!sample.getDeviceCode().contains(searchParams.getDeviceCode())) {
				return false;
			}
		}
		if (searchParams.getProductModel() != null && !searchParams.getProductModel().isEmpty()) {
			if (!sample.getProductModel().contains(searchParams.getProductModel())) {
				return false;
			}
		}
		if (searchParams.getSample() != null) {
			if (!searchParams.getSample().equals(sample.getSample())) {
				return false;
			}
		}
		return true;
	}

}
