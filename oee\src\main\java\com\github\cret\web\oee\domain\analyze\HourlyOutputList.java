package com.github.cret.web.oee.domain.analyze;

import java.util.List;

/**
 * 设备每小时产出（理论和实际产出）
 */
public class HourlyOutputList {

	// 每小时实际产出
	List<HourlyOutput> actualOutputs;

	// 每小时理论产出
	List<HourlyOutput> theoreticalOutputs;

	// 当前产品理论产出
	Integer currentProductTheoreticalOutput;

	public List<HourlyOutput> getActualOutputs() {
		return actualOutputs;
	}

	public void setActualOutputs(List<HourlyOutput> actualOutputs) {
		this.actualOutputs = actualOutputs;
	}

	public List<HourlyOutput> getTheoreticalOutputs() {
		return theoreticalOutputs;
	}

	public void setTheoreticalOutputs(List<HourlyOutput> theoreticalOutputs) {
		this.theoreticalOutputs = theoreticalOutputs;
	}

	public Integer getCurrentProductTheoreticalOutput() {
		return currentProductTheoreticalOutput;
	}

	public void setCurrentProductTheoreticalOutput(Integer currentProductTheoreticalOutput) {
		this.currentProductTheoreticalOutput = currentProductTheoreticalOutput;
	}

}
