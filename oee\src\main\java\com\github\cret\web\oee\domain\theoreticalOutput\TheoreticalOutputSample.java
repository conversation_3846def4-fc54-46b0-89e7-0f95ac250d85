
package com.github.cret.web.oee.domain.theoreticalOutput;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 理论产出样品
 */
@ExcelIgnoreUnannotated
public class TheoreticalOutputSample {

	// 线体编码
	@ExcelProperty("线体编码")
	String lineCode;

	@ExcelProperty("设备名称")
	String deviceName;

	// 设备编码
	@ExcelProperty("设备编码")
	String deviceCode;

	Integer deviceSort;

	// 产品名称
	@ExcelProperty("产品名称")
	String productModel;

	// 样品
	// @ExcelProperty("是否样品")
	Boolean sample;

	public String getLineCode() {
		return lineCode;
	}

	public void setLineCode(String lineCode) {
		this.lineCode = lineCode;
	}

	public String getDeviceName() {
		return deviceName;
	}

	public void setDeviceName(String deviceName) {
		this.deviceName = deviceName;
	}

	public String getDeviceCode() {
		return deviceCode;
	}

	public void setDeviceCode(String deviceCode) {
		this.deviceCode = deviceCode;
	}

	public Integer getDeviceSort() {
		return deviceSort;
	}

	public void setDeviceSort(Integer deviceSort) {
		this.deviceSort = deviceSort;
	}

	public String getProductModel() {
		return productModel;
	}

	public void setProductModel(String productModel) {
		this.productModel = productModel;
	}

	public Boolean getSample() {
		return sample;
	}

	public void setSample(Boolean sample) {
		this.sample = sample;
	}

}
