package com.github.cret.web.oee.service;

import java.util.List;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.domain.theoreticalOutput.TheoreticalOutputSample;

public interface TheoreticalOutputSampleService {

	/**
	 * 依据设备编码获取理论产出样品
	 */
	List<TheoreticalOutputSample> getTheoreticalOutputSamples();

	/**
	 * 分页查询理论产出样品
	 */
	PageList<TheoreticalOutputSample> page(PageableParam<TheoreticalOutputSample> param);

	/**
	 * 查询理论产出样品列表
	 */
	List<TheoreticalOutputSample> findList(TheoreticalOutputSample param);

}
