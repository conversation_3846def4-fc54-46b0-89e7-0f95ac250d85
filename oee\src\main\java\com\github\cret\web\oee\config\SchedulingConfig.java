package com.github.cret.web.oee.config;

import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class SchedulingConfig implements SchedulingConfigurer {

	@Override
	public void configureTasks(@NonNull ScheduledTaskRegistrar taskRegistrar) {
		// 设置一个大小为10的线程池来执行所有定时任务
		taskRegistrar.setScheduler(Executors.newScheduledThreadPool(10));
	}

	/**
	 * OEE计算专用线程池
	 */
	@Bean("oeeCalculationExecutor")
	public ThreadPoolTaskExecutor oeeCalculationExecutor() {
		ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
		// 核心线程数：CPU核心数
		executor.setCorePoolSize(Runtime.getRuntime().availableProcessors());
		// 最大线程数：CPU核心数的2倍
		executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2);
		// 队列容量
		executor.setQueueCapacity(100);
		// 线程名前缀
		executor.setThreadNamePrefix("OEE-Calc-");
		// 空闲线程存活时间
		executor.setKeepAliveSeconds(60);
		// 拒绝策略：调用者运行
		executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
		// 等待所有任务完成后再关闭线程池
		executor.setWaitForTasksToCompleteOnShutdown(true);
		// 等待时间
		executor.setAwaitTerminationSeconds(60);
		executor.initialize();
		return executor;
	}

}