package com.github.cret.web.oee.service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import org.springframework.web.multipart.MultipartFile;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.oee.document.Device;
import com.github.cret.web.oee.document.PlannedCapacity;
import com.github.cret.web.oee.document.analyze.TheoreticalOutput;
import com.github.cret.web.oee.domain.analyze.DeviceProductModelKey;
import com.github.cret.web.oee.domain.theoreticalOutput.TheoreticalOutputSample;

public interface TheoreticalOutputService {

	// 保存理论产出
	TheoreticalOutput save(TheoreticalOutput theoreticalOutput);

	// 分页查询理论产出
	PageList<TheoreticalOutput> page(PageableParam<TheoreticalOutput> param);

	// 查询理论产出列表
	List<TheoreticalOutput> findList(TheoreticalOutput param);

	// 查询指定设备和产品型号的理论产出
	TheoreticalOutput findByDeviceCodeAndProductModel(String deviceCode, String productModel);

	/**
	 * 模糊查询指定产品型号的理论产出
	 * @param deviceCode 设备编码
	 * @param productModel 产品名称
	 * @return 理论信息
	 */
	TheoreticalOutput findByDeviceCodeAndProductModelContaining(String deviceCode, String productModel);

	/**
	 * 根据产品名称获取最大的理论ct
	 * @param productModel
	 * @return
	 */
	Double findMaxCtByProductModelAndLineCode(String productModel, String lineCode);

	TheoreticalOutput findMaxCtInfoByProductModelAndLineCode(String productModel, String lineCode);

	// 根据id查询理论产出
	TheoreticalOutput findById(String id);

	// 查询所有理论产出
	List<TheoreticalOutput> findAll();

	// 根据id删除理论产出
	void deleteById(String id);

	/**
	 * 批量删除理论产能
	 * @param ids
	 */
	void batchDelete(List<String> ids);

	// 导入理论产出
	void importExcel(MultipartFile file) throws IOException;

	// 获取最大CT设备依据产品型号和生产编码
	String getMaxCtDeviceByProductModelAndLineCode(String lineCode, String productModel);

	Map<DeviceProductModelKey, TheoreticalOutput> batchFindByDeviceCodesAndProductModels(
			List<DeviceProductModelKey> keys);

	List<PlannedCapacity> findPlannedCapacityByGrouping(String lineCode, String productModel);

	List<PlannedCapacity> findPlannedCapacityByGrouping(String lineCode, List<String> productModel);

	/**
	 * 依据设备编码获取理论产出样品
	 * @param deviceCode
	 * @return
	 */
	List<TheoreticalOutputSample> getTheoreticalOutputSamples(Device device);

}